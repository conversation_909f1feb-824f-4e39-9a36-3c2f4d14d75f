# 视觉脚本节点实现示例

## 概述

本文档提供视觉脚本节点的具体实现示例，展示如何开发符合DL引擎标准的视觉脚本节点。

## 节点基础结构

### 1. 节点类型选择

根据功能选择合适的基类：

```typescript
// 流程控制节点 - 有执行流输入输出
export class MyFlowNode extends FlowNode {
  // 实现流程控制逻辑
}

// 函数节点 - 纯数据处理，无执行流
export class MyFunctionNode extends FunctionNode {
  // 实现数据转换逻辑
}

// 事件节点 - 响应外部事件
export class MyEventNode extends EventNode {
  // 实现事件监听逻辑
}

// 异步节点 - 处理异步操作
export class MyAsyncNode extends AsyncNode {
  // 实现异步操作逻辑
}
```

### 2. 节点实现模板

```typescript
/**
 * 示例节点 - 播放动画
 */
export class PlayAnimationNode extends FlowNode {
  constructor(options: FlowNodeOptions) {
    super({
      ...options,
      outputFlowNames: ['success', 'failed', 'completed']
    });

    // 设置节点元数据
    if (!this.metadata.name) {
      this.metadata.name = '播放动画';
    }
    if (!this.metadata.description) {
      this.metadata.description = '播放指定实体的动画';
    }
    if (!this.metadata.category) {
      this.metadata.category = '动画节点';
    }
  }

  /**
   * 初始化输入输出插槽
   */
  protected initializeSockets(): void {
    // 输入插槽
    this.addInput({
      name: 'entity',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'Entity',
      description: '要播放动画的实体',
      required: true
    });

    this.addInput({
      name: 'animationName',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '动画名称',
      defaultValue: '',
      required: true
    });

    this.addInput({
      name: 'loop',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'boolean',
      description: '是否循环播放',
      defaultValue: false
    });

    this.addInput({
      name: 'speed',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: '播放速度倍率',
      defaultValue: 1.0,
      min: 0.1,
      max: 10.0
    });

    // 输出插槽
    this.addOutput({
      name: 'animationInstance',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'AnimationInstance',
      description: '动画实例对象'
    });

    this.addOutput({
      name: 'duration',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'number',
      description: '动画总时长（秒）'
    });
  }

  /**
   * 验证节点配置
   */
  public validate(): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    // 检查必需输入
    const entity = this.getInputValue('entity');
    if (!entity) {
      errors.push('缺少实体输入');
    }

    const animationName = this.getInputValue('animationName');
    if (!animationName || animationName.trim() === '') {
      errors.push('动画名称不能为空');
    }

    const speed = this.getInputValue('speed');
    if (speed <= 0) {
      errors.push('播放速度必须大于0');
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  /**
   * 执行节点逻辑
   */
  protected async executeImpl(): Promise<any> {
    try {
      // 获取输入值
      const entity = this.getInputValue('entity');
      const animationName = this.getInputValue('animationName');
      const loop = this.getInputValue('loop');
      const speed = this.getInputValue('speed');

      // 获取动画组件
      const animationComponent = entity.getComponent('Animation');
      if (!animationComponent) {
        throw new Error('实体没有动画组件');
      }

      // 检查动画是否存在
      if (!animationComponent.hasAnimation(animationName)) {
        throw new Error(`动画 "${animationName}" 不存在`);
      }

      // 播放动画
      const animationInstance = animationComponent.play(animationName, {
        loop,
        speed
      });

      // 设置输出值
      this.setOutputValue('animationInstance', animationInstance);
      this.setOutputValue('duration', animationInstance.duration);

      // 监听动画完成事件
      animationInstance.onComplete(() => {
        this.triggerOutput('completed');
      });

      // 触发成功输出
      this.triggerOutput('success');

      return animationInstance;

    } catch (error) {
      // 记录错误
      this.debug('播放动画失败', error);
      
      // 触发失败输出
      this.triggerOutput('failed');
      
      throw error;
    }
  }

  /**
   * 清理资源
   */
  public dispose(): void {
    // 停止正在播放的动画
    const animationInstance = this.getOutputValue('animationInstance');
    if (animationInstance && animationInstance.isPlaying()) {
      animationInstance.stop();
    }

    super.dispose();
  }
}
```

## 不同类型节点示例

### 1. 函数节点示例 - 向量运算

```typescript
/**
 * 向量加法节点
 */
export class VectorAddNode extends FunctionNode {
  constructor(options: NodeOptions) {
    super(options);
  }

  protected initializeSockets(): void {
    this.addInput({
      name: 'vectorA',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'Vector3',
      description: '向量A',
      defaultValue: { x: 0, y: 0, z: 0 }
    });

    this.addInput({
      name: 'vectorB',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'Vector3',
      description: '向量B',
      defaultValue: { x: 0, y: 0, z: 0 }
    });

    this.addOutput({
      name: 'result',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'Vector3',
      description: '相加结果'
    });
  }

  protected executeImpl(): any {
    const vectorA = this.getInputValue('vectorA');
    const vectorB = this.getInputValue('vectorB');

    const result = {
      x: vectorA.x + vectorB.x,
      y: vectorA.y + vectorB.y,
      z: vectorA.z + vectorB.z
    };

    this.setOutputValue('result', result);
    return result;
  }
}
```

### 2. 事件节点示例 - 键盘输入

```typescript
/**
 * 按键按下事件节点
 */
export class KeyDownNode extends EventNode {
  private keyCode: string = '';
  private isListening: boolean = false;

  constructor(options: NodeOptions) {
    super(options);
  }

  protected initializeSockets(): void {
    this.addInput({
      name: 'keyCode',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '监听的按键代码',
      defaultValue: 'Space'
    });

    this.addOutput({
      name: 'pressed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '按键按下时触发'
    });

    this.addOutput({
      name: 'keyInfo',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'object',
      description: '按键信息'
    });
  }

  protected executeImpl(): any {
    this.keyCode = this.getInputValue('keyCode');
    this.startListening();
    return null;
  }

  private startListening(): void {
    if (this.isListening) return;

    this.isListening = true;
    
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.code === this.keyCode) {
        const keyInfo = {
          code: event.code,
          key: event.key,
          timestamp: Date.now(),
          ctrlKey: event.ctrlKey,
          shiftKey: event.shiftKey,
          altKey: event.altKey
        };

        this.setOutputValue('keyInfo', keyInfo);
        this.triggerOutput('pressed');
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    
    // 保存事件处理器引用以便清理
    this.eventHandlers = this.eventHandlers || [];
    this.eventHandlers.push({
      element: document,
      event: 'keydown',
      handler: handleKeyDown
    });
  }

  public dispose(): void {
    this.stopListening();
    super.dispose();
  }

  private stopListening(): void {
    if (!this.isListening) return;

    this.isListening = false;
    
    // 清理事件监听器
    if (this.eventHandlers) {
      this.eventHandlers.forEach(({ element, event, handler }) => {
        element.removeEventListener(event, handler);
      });
      this.eventHandlers = [];
    }
  }
}
```

### 3. 异步节点示例 - HTTP请求

```typescript
/**
 * HTTP GET请求节点
 */
export class HTTPGetNode extends AsyncNode {
  constructor(options: NodeOptions) {
    super({
      ...options,
      outputFlowNames: ['success', 'error']
    });
  }

  protected initializeSockets(): void {
    this.addInput({
      name: 'url',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '请求URL',
      required: true
    });

    this.addInput({
      name: 'headers',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'object',
      description: '请求头',
      defaultValue: {}
    });

    this.addInput({
      name: 'timeout',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: '超时时间（毫秒）',
      defaultValue: 5000
    });

    this.addOutput({
      name: 'response',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'object',
      description: '响应数据'
    });

    this.addOutput({
      name: 'status',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'number',
      description: '状态码'
    });
  }

  protected async executeImpl(): Promise<any> {
    const url = this.getInputValue('url');
    const headers = this.getInputValue('headers');
    const timeout = this.getInputValue('timeout');

    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), timeout);

      const response = await fetch(url, {
        method: 'GET',
        headers,
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      const data = await response.json();

      this.setOutputValue('response', data);
      this.setOutputValue('status', response.status);

      this.triggerOutput('success');
      return data;

    } catch (error) {
      this.debug('HTTP请求失败', error);
      this.triggerOutput('error');
      throw error;
    }
  }
}
```

## 节点注册

### 注册函数示例

```typescript
/**
 * 注册动画节点
 */
export function registerAnimationNodes(registry: NodeRegistry): void {
  // 播放动画节点
  registry.registerNodeType({
    type: 'animation/play',
    name: '播放动画',
    category: '动画节点',
    description: '播放指定实体的动画',
    factory: (options: NodeOptions) => new PlayAnimationNode(options),
    tags: ['动画', '播放', '实体'],
    icon: 'play-circle',
    color: '#52c41a'
  });

  // 停止动画节点
  registry.registerNodeType({
    type: 'animation/stop',
    name: '停止动画',
    category: '动画节点',
    description: '停止指定的动画播放',
    factory: (options: NodeOptions) => new StopAnimationNode(options),
    tags: ['动画', '停止'],
    icon: 'stop',
    color: '#ff4d4f'
  });

  // ... 其他节点注册
}
```

## 测试示例

### 单元测试

```typescript
describe('PlayAnimationNode', () => {
  let node: PlayAnimationNode;
  let mockEntity: Entity;
  let mockAnimationComponent: any;

  beforeEach(() => {
    // 创建模拟对象
    mockAnimationComponent = {
      hasAnimation: jest.fn(),
      play: jest.fn()
    };

    mockEntity = {
      getComponent: jest.fn().mockReturnValue(mockAnimationComponent)
    } as any;

    // 创建节点
    node = new PlayAnimationNode({
      id: 'test-node',
      position: { x: 0, y: 0 }
    });
  });

  afterEach(() => {
    node.dispose();
  });

  test('应该成功播放动画', async () => {
    // 设置输入
    node.setInputValue('entity', mockEntity);
    node.setInputValue('animationName', 'walk');
    node.setInputValue('loop', true);
    node.setInputValue('speed', 1.0);

    // 设置模拟返回值
    mockAnimationComponent.hasAnimation.mockReturnValue(true);
    mockAnimationComponent.play.mockReturnValue({
      duration: 2.0,
      isPlaying: () => true,
      stop: () => {},
      onComplete: (callback: Function) => {}
    });

    // 执行节点
    await node.execute();

    // 验证结果
    expect(mockAnimationComponent.play).toHaveBeenCalledWith('walk', {
      loop: true,
      speed: 1.0
    });

    expect(node.getOutputValue('duration')).toBe(2.0);
  });

  test('应该处理动画不存在的情况', async () => {
    // 设置输入
    node.setInputValue('entity', mockEntity);
    node.setInputValue('animationName', 'nonexistent');

    // 设置模拟返回值
    mockAnimationComponent.hasAnimation.mockReturnValue(false);

    // 执行节点并期望抛出错误
    await expect(node.execute()).rejects.toThrow('动画 "nonexistent" 不存在');
  });
});
```

## 最佳实践

### 1. 性能优化
- 避免在每帧执行中进行重复计算
- 使用缓存机制存储计算结果
- 及时清理不需要的资源

### 2. 错误处理
- 提供详细的错误信息
- 实现优雅的降级处理
- 记录调试信息

### 3. 用户体验
- 提供清晰的节点描述
- 设置合理的默认值
- 实现输入验证

### 4. 可维护性
- 保持代码简洁明了
- 添加详细的注释
- 遵循统一的编码规范

通过这些示例和最佳实践，开发者可以创建高质量、功能完整的视觉脚本节点，为用户提供强大的可视化编程体验。
